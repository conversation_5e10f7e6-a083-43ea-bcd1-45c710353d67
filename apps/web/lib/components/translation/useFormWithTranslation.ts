"use client"

import { useCallback, useMemo } from "react"
import { useTranslationConfirm } from "./useTranslationConfirm"
import type { TranslationChoice } from "./useTranslationConfirm"

/** 表单提交配置 */
interface FormSubmitConfig {
	/** 项目ID */
	projectId: string
	/** 内容类型，用于区分不同的表单 */
	contentType?: string
	/** 翻译确认对话框选项 */
	confirmOptions?: {
		title?: string
		description?: string
		confirmText?: string
		cancelText?: string
		rememberText?: string
	}
}

/** 原始提交函数类型 */
type OriginalSubmitFunction<T = any> = (data: T, options?: { isTranslation?: boolean }) => Promise<any>

/** 增强后的提交函数类型 */
type EnhancedSubmitFunction<T = any> = (data: T) => Promise<any>

/** 表单翻译集成结果 */
interface FormWithTranslationResult<T = any> {
	/** 增强后的提交函数 */
	handleSubmit: EnhancedSubmitFunction<T>
	/** 翻译确认对话框组件的 props */
	translationDialogProps: {
		open: boolean
		onOpenChange: (open: boolean) => void
		onConfirm: () => void
		onCancel: () => void
		rememberChoice: boolean
		onRememberChoiceChange: (checked: boolean) => void
		title?: string
		description?: string
		confirmText?: string
		cancelText?: string
		rememberText?: string
	}
	/** 当前翻译选择 */
	currentChoice: TranslationChoice
	/** 重置翻译选择 */
	resetTranslationChoice: () => void
}

/**
 * 表单翻译集成 Hook
 * 
 * 为表单提交添加翻译确认功能，自动处理用户选择和 isTranslation 参数
 * 
 * @param originalSubmit 原始的表单提交函数
 * @param config 配置选项
 * @returns 增强后的表单提交函数和翻译对话框相关状态
 * 
 * @example
 * ```tsx
 * // 在表单组件中使用
 * const { handleSubmit, translationDialogProps } = useFormWithTranslation(
 *   async (data, options) => {
 *     // 原始的提交逻辑
 *     return await updateSEOMetadata(data, options?.isTranslation)
 *   },
 *   {
 *     projectId: "project-123",
 *     contentType: "seo-metadata",
 *     confirmOptions: {
 *       title: "发起SEO翻译任务",
 *       description: "是否同时将SEO设置翻译到其他语言？",
 *     }
 *   }
 * )
 * 
 * // 在JSX中使用
 * <form onSubmit={form.handleSubmit(handleSubmit)}>
 *   // 表单内容
 * </form>
 * 
 * <TranslationConfirmDialog {...translationDialogProps} />
 * ```
 */
export function useFormWithTranslation<T = any>(
	originalSubmit: OriginalSubmitFunction<T>,
	config: FormSubmitConfig,
): FormWithTranslationResult<T> {
	const { projectId, contentType = "default", confirmOptions = {} } = config

	// 使用翻译确认 hook
	const translationConfirm = useTranslationConfirm({
		projectId,
		contentType,
	})

	// 增强后的提交函数
	const handleSubmit = useCallback(
		async (data: T): Promise<any> => {
			try {
				// 询问用户是否需要翻译
				const result = await translationConfirm.confirm({
					title: "发起翻译任务",
					description: "检测到您保存了内容，是否同时发起翻译任务？翻译任务将会把当前内容翻译到其他语言版本。",
					confirmText: "发起翻译",
					cancelText: "仅保存",
					rememberText: "记住我的选择",
					...confirmOptions,
				})

				// 调用原始提交函数，传递 isTranslation 参数
				return await originalSubmit(data, {
					isTranslation: result.confirmed,
				})
			} catch (error) {
				// 重新抛出错误，让调用方处理
				throw error
			}
		},
		[originalSubmit, translationConfirm, confirmOptions],
	)

	// 翻译对话框的 props
	const translationDialogProps = useMemo(
		() => ({
			open: translationConfirm.isOpen,
			onOpenChange: translationConfirm.onOpenChange,
			onConfirm: translationConfirm.onConfirm,
			onCancel: translationConfirm.onCancel,
			rememberChoice: translationConfirm.rememberChoice,
			onRememberChoiceChange: translationConfirm.onRememberChoiceChange,
			title: translationConfirm.title,
			description: translationConfirm.description,
			confirmText: translationConfirm.confirmText,
			cancelText: translationConfirm.cancelText,
			rememberText: translationConfirm.rememberText,
		}),
		[translationConfirm],
	)

	return {
		handleSubmit,
		translationDialogProps,
		currentChoice: translationConfirm.currentChoice,
		resetTranslationChoice: translationConfirm.reset,
	}
}

/**
 * 简化版本的表单翻译集成 Hook
 * 
 * 适用于不需要自定义配置的简单场景
 * 
 * @param originalSubmit 原始的表单提交函数
 * @param projectId 项目ID
 * @param contentType 内容类型
 * @returns 增强后的表单提交函数和翻译对话框相关状态
 */
export function useSimpleFormWithTranslation<T = any>(
	originalSubmit: OriginalSubmitFunction<T>,
	projectId: string,
	contentType?: string,
): FormWithTranslationResult<T> {
	return useFormWithTranslation(originalSubmit, {
		projectId,
		contentType,
	})
}
