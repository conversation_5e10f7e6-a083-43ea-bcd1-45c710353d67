"use client"

import { useState, useCallback, useEffect, useMemo } from "react"

/** 翻译确认选择类型 */
export type TranslationChoice = "always" | "never" | "ask"

/** 翻译确认配置 */
interface TranslationConfirmConfig {
	/** 存储键名前缀 */
	storageKeyPrefix?: string
	/** 项目ID（用于区分不同项目的设置） */
	projectId?: string
	/** 内容类型（用于区分不同内容类型的设置） */
	contentType?: string
}

/** 翻译确认选项 */
interface TranslationConfirmOptions {
	/** 对话框标题 */
	title?: string
	/** 对话框描述 */
	description?: string
	/** 确认按钮文本 */
	confirmText?: string
	/** 取消按钮文本 */
	cancelText?: string
	/** 记住选择文本 */
	rememberText?: string
}

/** 翻译确认结果 */
interface TranslationConfirmResult {
	/** 是否确认翻译 */
	confirmed: boolean
	/** 是否记住选择 */
	remembered: boolean
}

/**
 * 翻译确认 Hook
 * 
 * 提供翻译确认对话框的状态管理和 localStorage 持久化功能
 * 
 * @param config 配置选项
 * @returns 翻译确认相关的状态和方法
 */
export function useTranslationConfirm(config: TranslationConfirmConfig = {}) {
	const {
		storageKeyPrefix = "translation-confirm",
		projectId,
		contentType = "default",
	} = config

	// 生成存储键名
	const storageKey = useMemo(() => {
		const parts = [storageKeyPrefix]
		if (projectId) parts.push(projectId)
		parts.push(contentType)
		return parts.join("-")
	}, [storageKeyPrefix, projectId, contentType])

	// 对话框状态
	const [isOpen, setIsOpen] = useState(false)
	const [rememberChoice, setRememberChoice] = useState(false)
	const [options, setOptions] = useState<TranslationConfirmOptions>({})
	const [resolveRef, setResolveRef] = useState<
		((result: TranslationConfirmResult) => void) | null
	>(null)

	// 从 localStorage 获取用户选择
	const getSavedChoice = useCallback((): TranslationChoice => {
		if (typeof window === "undefined") return "ask"
		
		try {
			const saved = localStorage.getItem(storageKey)
			if (saved && ["always", "never", "ask"].includes(saved)) {
				return saved as TranslationChoice
			}
		} catch (error) {
			console.warn("Failed to read translation choice from localStorage:", error)
		}
		return "ask"
	}, [storageKey])

	// 保存用户选择到 localStorage
	const saveChoice = useCallback(
		(choice: TranslationChoice) => {
			if (typeof window === "undefined") return

			try {
				localStorage.setItem(storageKey, choice)
			} catch (error) {
				console.warn("Failed to save translation choice to localStorage:", error)
			}
		},
		[storageKey],
	)

	// 清除保存的选择
	const clearSavedChoice = useCallback(() => {
		if (typeof window === "undefined") return

		try {
			localStorage.removeItem(storageKey)
		} catch (error) {
			console.warn("Failed to clear translation choice from localStorage:", error)
		}
	}, [storageKey])

	// 处理确认操作
	const handleConfirm = useCallback(() => {
		if (resolveRef) {
			const choice: TranslationChoice = rememberChoice ? "always" : "ask"
			if (rememberChoice) {
				saveChoice(choice)
			}
			resolveRef({ confirmed: true, remembered: rememberChoice })
			setResolveRef(null)
		}
		setIsOpen(false)
		setRememberChoice(false)
	}, [resolveRef, rememberChoice, saveChoice])

	// 处理取消操作
	const handleCancel = useCallback(() => {
		if (resolveRef) {
			const choice: TranslationChoice = rememberChoice ? "never" : "ask"
			if (rememberChoice) {
				saveChoice(choice)
			}
			resolveRef({ confirmed: false, remembered: rememberChoice })
			setResolveRef(null)
		}
		setIsOpen(false)
		setRememberChoice(false)
	}, [resolveRef, rememberChoice, saveChoice])

	// 处理对话框关闭
	const handleOpenChange = useCallback(
		(open: boolean) => {
			if (!open && resolveRef) {
				// 如果对话框被关闭但没有明确选择，视为取消
				resolveRef({ confirmed: false, remembered: false })
				setResolveRef(null)
				setRememberChoice(false)
			}
			setIsOpen(open)
		},
		[resolveRef],
	)

	// 主要的确认方法
	const confirm = useCallback(
		(confirmOptions: TranslationConfirmOptions = {}): Promise<TranslationConfirmResult> => {
			return new Promise((resolve) => {
				const savedChoice = getSavedChoice()

				// 如果用户已经保存了选择，直接返回结果
				if (savedChoice === "always") {
					resolve({ confirmed: true, remembered: true })
					return
				}
				if (savedChoice === "never") {
					resolve({ confirmed: false, remembered: true })
					return
				}

				// 否则显示对话框
				setOptions(confirmOptions)
				setResolveRef(() => resolve)
				setIsOpen(true)
			})
		},
		[getSavedChoice],
	)

	// 重置设置（清除记住的选择）
	const reset = useCallback(() => {
		clearSavedChoice()
	}, [clearSavedChoice])

	// 获取当前保存的选择
	const currentChoice = useMemo(() => getSavedChoice(), [getSavedChoice])

	return {
		// 状态
		isOpen,
		rememberChoice,
		currentChoice,
		
		// 方法
		confirm,
		reset,
		
		// 对话框处理方法
		onOpenChange: handleOpenChange,
		onConfirm: handleConfirm,
		onCancel: handleCancel,
		onRememberChoiceChange: setRememberChoice,
		
		// 对话框选项
		...options,
	}
}
