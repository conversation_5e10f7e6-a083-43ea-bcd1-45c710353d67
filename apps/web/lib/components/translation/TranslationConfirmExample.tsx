"use client"

import { useState } from "react"
import { Button } from "@repo/ui/components"
import { TranslationConfirmDialog } from "./TranslationConfirmDialog"
import { useTranslationConfirm } from "./useTranslationConfirm"
import { Save, RotateCcw } from "lucide-react"
import { toast } from "sonner"

/**
 * 翻译确认对话框使用示例
 * 
 * 展示如何在实际项目中集成和使用翻译确认功能
 */
export function TranslationConfirmExample() {
	const [isSaving, setIsSaving] = useState(false)

	// 使用翻译确认 hook
	const {
		isOpen,
		rememberChoice,
		currentChoice,
		confirm,
		reset,
		onOpenChange,
		onConfirm,
		onCancel,
		onRememberChoiceChange,
		title,
		description,
		confirmText,
		cancelText,
		rememberText,
	} = useTranslationConfirm({
		projectId: "example-project",
		contentType: "article",
	})

	// 模拟保存操作
	const handleSave = async () => {
		setIsSaving(true)

		try {
			// 询问用户是否需要翻译
			const result = await confirm({
				title: "发起翻译任务",
				description: "检测到您保存了文章内容，是否同时发起翻译任务？翻译任务将会把当前内容翻译到其他语言版本。",
				confirmText: "发起翻译",
				cancelText: "仅保存",
				rememberText: "记住我的选择，下次不再询问",
			})

			// 模拟保存内容
			await new Promise((resolve) => setTimeout(resolve, 1000))

			if (result.confirmed) {
				// 用户选择了翻译
				toast.success("内容已保存，翻译任务已创建")
				console.log("保存内容并创建翻译任务")
			} else {
				// 用户选择了仅保存
				toast.success("内容已保存")
				console.log("仅保存内容")
			}

			if (result.remembered) {
				toast.info("已记住您的选择")
			}
		} catch (error) {
			toast.error("保存失败")
			console.error("保存失败:", error)
		} finally {
			setIsSaving(false)
		}
	}

	// 重置用户选择
	const handleReset = () => {
		reset()
		toast.success("已重置翻译选择设置")
	}

	return (
		<div className="p-6 max-w-md mx-auto space-y-4">
			<div className="space-y-2">
				<h3 className="text-lg font-semibold">翻译确认示例</h3>
				<p className="text-sm text-muted-foreground">
					点击保存按钮体验翻译确认功能
				</p>
			</div>

			<div className="space-y-2">
				<div className="text-sm">
					<span className="font-medium">当前设置：</span>
					<span className="ml-2 px-2 py-1 bg-muted rounded text-xs">
						{currentChoice === "always" && "总是翻译"}
						{currentChoice === "never" && "从不翻译"}
						{currentChoice === "ask" && "每次询问"}
					</span>
				</div>
			</div>

			<div className="flex gap-2">
				<Button
					onClick={handleSave}
					loading={isSaving}
					disabled={isSaving}
					className="flex-1"
				>
					<Save className="size-4" />
					保存内容
				</Button>

				<Button
					variant="outline"
					onClick={handleReset}
					disabled={isSaving}
				>
					<RotateCcw className="size-4" />
					重置设置
				</Button>
			</div>

			{/* 翻译确认对话框 */}
			<TranslationConfirmDialog
				open={isOpen}
				onOpenChange={onOpenChange}
				onConfirm={onConfirm}
				onCancel={onCancel}
				rememberChoice={rememberChoice}
				onRememberChoiceChange={onRememberChoiceChange}
				title={title}
				description={description}
				confirmText={confirmText}
				cancelText={cancelText}
				rememberText={rememberText}
			/>
		</div>
	)
}

/**
 * 简化版本的使用示例
 * 
 * 展示最简单的集成方式
 */
export function SimpleTranslationConfirmExample() {
	const translationConfirm = useTranslationConfirm({
		projectId: "simple-example",
	})

	const handleSaveWithTranslation = async () => {
		const result = await translationConfirm.confirm()
		
		if (result.confirmed) {
			console.log("保存并翻译")
		} else {
			console.log("仅保存")
		}
	}

	return (
		<div className="p-4">
			<Button onClick={handleSaveWithTranslation}>
				保存内容
			</Button>

			<TranslationConfirmDialog
				open={translationConfirm.isOpen}
				onOpenChange={translationConfirm.onOpenChange}
				onConfirm={translationConfirm.onConfirm}
				onCancel={translationConfirm.onCancel}
				rememberChoice={translationConfirm.rememberChoice}
				onRememberChoiceChange={translationConfirm.onRememberChoiceChange}
			/>
		</div>
	)
}
