import { createSingleTranslationTask } from "@/lib/services/TranslationTaskService"
import { authUser, hasProjectAccess } from "@repo/auth/server"
import { prisma } from "@repo/db"
import { getLogger } from "@repo/logger"
import { R } from "@repo/utils/server"
import { AutoRouter } from "itty-router"
import { NextRequest } from "next/server"
import {
	getSettingSchema,
	queryParamsSchema,
	upsertSettingSchema,
} from "./schemas"

const logger = getLogger("ProjectLocaleSiteSettings")

// 创建路由处理器
const router = AutoRouter({ base: "/api/project-locale-site-settings" })

// 获取项目设置
router.get("/all", async (request) => {
	try {
		const { projectId, type, locale } = queryParamsSchema.parse(request.query)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 构建查询条件
		const where: any = { projectId }
		if (type) where.type = type
		if (locale) where.locale = locale

		// 查询设置
		const settings = await prisma.projectLocaleSiteSetting.findMany({
			where,
			orderBy: { updatedAt: "desc" },
		})

		return R.ok(settings)
	} catch (error) {
		logger.error("获取项目设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

// 获取单个设置
router.get("/", async (request) => {
	try {
		const { projectId, type, locale } = getSettingSchema.parse(request.query)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 查询设置
		const setting = await prisma.projectLocaleSiteSetting.findFirst({
			where: {
				projectId,
				type,
				locale,
			},
		})

		if (!setting) {
			return R.ok({ content: null })
		}

		return R.ok(setting)
	} catch (error) {
		logger.error("获取单个设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

// 创建或更新设置
router.post("/", async (request) => {
	try {
		const body = await request.json()
		const { projectId, locale, type, content, text, status, isTranslation } =
			upsertSettingSchema.parse(body)

		// 验证项目访问权限
		await hasProjectAccess(projectId)

		// 获取当前用户
		const user = await authUser()
		if (!user) {
			return R.error("用户未登录", { status: 401 })
		}

		// 创建或更新设置
		const result = await prisma.projectLocaleSiteSetting.upsert({
			where: {
				project_locale_site_setting_unique: {
					projectId,
					locale,
					type,
				},
			},
			update: {
				content,
				text,
				status,
				updatedAt: new Date(),
			},
			create: {
				projectId,
				locale,
				type,
				content,
				text,
				status,
			},
		})

		// 创建翻译任务
		if (isTranslation) {
			await createSingleTranslationTask(
				`ProjectSiteSetting_${type}`,
				projectId,
				user.id,
				{
					contentType: `ProjectSiteSetting_${type}`,
					contentId: result.id,
				},
			)
		}

		return R.ok({
			...result,
		})
	} catch (error) {
		logger.error("保存设置时出错:", error)
		const errorMessage = error instanceof Error ? error.message : String(error)
		return R.error(errorMessage)
	}
})

// 404处理
router.all("*", () => {
	return Response.json(
		{
			success: false,
			code: 404,
			message: "API endpoint not found",
		},
		{ status: 404 },
	)
})

export const GET = async (request: NextRequest) => router.fetch(request)
export const POST = async (request: NextRequest) => router.fetch(request)
export const PUT = async (request: NextRequest) => router.fetch(request)
export const DELETE = async (request: NextRequest) => router.fetch(request)
