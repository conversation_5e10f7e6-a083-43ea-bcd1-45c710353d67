"use client"

import { useState, useEffect } from "react"
import { toast } from "sonner"
import { fetchGet, fetchPost } from "@repo/utils/react"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,

  Input,
  Textarea,
  Label,
  Switch,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@repo/ui/components"
import { Plus, Edit, Trash2, RefreshCw } from "lucide-react"
import {
  PROMPT_TEMPLATE_TYPE,
  PROMPT_TYPE_LABELS,
  DEFAULT_PROMPT_CONTENTS,
  type PromptTemplateType
} from "@repo/shared-types"

interface SystemPromptTemplate {
  id: string
  type: string
  name: string
  description?: string
  content: string
  isEnabled: boolean
  isDefault: boolean
  createdAt: string
  updatedAt: string
}

interface PromptFormData {
  id?: string
  type: PromptTemplateType
  name: string
  description: string
  content: string
  isEnabled: boolean
}

export default function SystemPromptsPage() {
  const [prompts, setPrompts] = useState<SystemPromptTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [typeFilter, setTypeFilter] = useState<string>("all")

  // 表单状态
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState<PromptFormData>({
    type: PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION,
    name: "",
    description: "",
    content: "",
    isEnabled: true
  })
  const [formLoading, setFormLoading] = useState(false)

  // 获取系统提示词列表
  const fetchPrompts = async () => {
    try {
      setLoading(true)

      const params = new URLSearchParams({
        page: "1",
        limit: "100" // 获取所有系统提示词
      })

      if (typeFilter && typeFilter !== "all") params.append("type", typeFilter)

      const response = await fetchGet(`/api/system-prompts?${params}`)
      setPrompts(response.data)
    } catch (error) {
      console.error("获取系统提示词列表失败:", error)
      toast.error("获取系统提示词列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 保存系统提示词
  const savePrompt = async () => {
    try {
      setFormLoading(true)

      const endpoint = formData.id ? "/api/system-prompts/update" : "/api/system-prompts/create"
      await fetchPost(endpoint, formData)

      toast.success(formData.id ? "系统提示词更新成功" : "系统提示词创建成功")
      setShowForm(false)
      resetForm()
      fetchPrompts()
    } catch (error) {
      console.error("保存系统提示词失败:", error)
      toast.error("保存系统提示词失败")
    } finally {
      setFormLoading(false)
    }
  }

  // 删除系统提示词
  const deletePrompt = async (id: string) => {
    if (!confirm("确定要删除这个系统提示词吗？")) return

    try {
      await fetchPost("/api/system-prompts/delete", { id })
      toast.success("系统提示词删除成功")
      fetchPrompts()
    } catch (error) {
      console.error("删除系统提示词失败:", error)
      toast.error("删除系统提示词失败")
    }
  }

  // 编辑系统提示词
  const editPrompt = (prompt: SystemPromptTemplate) => {
    setFormData({
      id: prompt.id,
      type: prompt.type as PromptTemplateType,
      name: prompt.name,
      description: prompt.description || "",
      content: prompt.content,
      isEnabled: prompt.isEnabled
    })
    setShowForm(true)
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      type: PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION,
      name: "",
      description: "",
      content: "",
      isEnabled: true
    })
  }

  // 当类型改变时，自动填充默认内容
  const handleTypeChange = (type: PromptTemplateType) => {
    setFormData(prev => ({
      ...prev,
      type,
      content: DEFAULT_PROMPT_CONTENTS[type] || ""
    }))
  }

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString("zh-CN")
  }

  // 初始化系统提示词
  const initializePrompts = async () => {
    if (!confirm("确定要初始化系统默认提示词吗？这将创建所有缺失的默认提示词。")) return

    try {
      setLoading(true)
      const response = await fetchPost("/api/system-prompts/initialize", {})
      toast.success(`初始化完成：创建 ${response.summary.created} 个，已存在 ${response.summary.exists} 个`)
      fetchPrompts()
    } catch (error) {
      console.error("初始化系统提示词失败:", error)
      toast.error("初始化系统提示词失败")
    } finally {
      setLoading(false)
    }
  }

  // 过滤提示词
  const filteredPrompts = prompts.filter(prompt =>
    typeFilter === "all" || prompt.type === typeFilter
  )

  useEffect(() => {
    fetchPrompts()
  }, [typeFilter])

  return (
    <div className="container py-6">
      <div>
        <h1 className="text-2xl font-bold mb-2">系统提示词管理</h1>
        <p className="text-muted-foreground">管理系统级的AI提示词模板，为所有项目提供默认配置</p>
      </div>

      {/* 操作栏 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>系统提示词列表</CardTitle>
            <div className="flex items-center gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="类型筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {Object.entries(PROMPT_TYPE_LABELS).map(([type, label]) => (
                    <SelectItem key={type} value={type}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm" onClick={fetchPrompts}>
                <RefreshCw className="h-4 w-4 mr-1" />
                刷新
              </Button>

              <Button variant="outline" size="sm" onClick={initializePrompts}>
                初始化默认提示词
              </Button>

              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-1" />
                新建系统提示词
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : filteredPrompts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">暂无系统提示词</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>名称</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredPrompts.map((prompt) => (
                  <TableRow key={prompt.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{prompt.name}</div>
                        {prompt.description && (
                          <div className="text-sm text-muted-foreground">{prompt.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {PROMPT_TYPE_LABELS[prompt.type as PromptTemplateType] || prompt.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Badge variant={prompt.isEnabled ? "success" : "secondary"}>
                          {prompt.isEnabled ? "启用" : "禁用"}
                        </Badge>
                        {prompt.isDefault && (
                          <Badge variant="outline">系统默认</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{formatTime(prompt.updatedAt)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => editPrompt(prompt)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>

                        {!prompt.isDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deletePrompt(prompt.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* 系统提示词表单对话框 */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{formData.id ? "编辑系统提示词" : "新建系统提示词"}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">提示词类型</Label>
                <Select
                  value={formData.type}
                  onValueChange={handleTypeChange}
                  disabled={!!formData.id}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(PROMPT_TYPE_LABELS).map(([type, label]) => (
                      <SelectItem key={type} value={type}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="name">提示词名称</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="请输入提示词名称"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">描述</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="请输入提示词描述（可选）"
              />
            </div>

            <div>
              <Label htmlFor="content">提示词内容</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="请输入提示词内容"
                rows={15}
                className="font-mono text-sm"
              />
              <div className="text-xs text-muted-foreground mt-1">
                支持变量：{"{sourceLanguage}"}, {"{targetLanguage}"}, {"{content}"} 等
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isEnabled"
                checked={formData.isEnabled}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isEnabled: checked }))}
              />
              <Label htmlFor="isEnabled">启用此提示词</Label>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowForm(false)}>
                取消
              </Button>
              <Button onClick={savePrompt} disabled={formLoading}>
                {formLoading ? "保存中..." : "保存"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
