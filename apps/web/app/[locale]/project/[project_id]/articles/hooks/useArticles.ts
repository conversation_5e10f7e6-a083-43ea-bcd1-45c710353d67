"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { fetchGet, fetchPost, fetchPut } from "@repo/utils/react"
import { nanoid } from "nanoid"
import { useEffect, useRef, useState } from "react"
import { toast } from "sonner"
import useSWR from "swr"
import {ArticlePost} from "@repo/shared-types"

/**
 * 生成唯一的文章ID
 * @returns 唯一的文章ID字符串
 */
const generateArticleId = (): string => {
	return `art_${nanoid(8)}`
}

/**
 * 获取文章管理的Hook
 */
export function useArticles(projectId: string) {
	// 使用 LanguageContext 获取语言信息
	const { defaultLanguage, currentLanguage, languages } =
		useTranslationContext()

	// 加载状态
	const [isLoading, setIsLoading] = useState<boolean>(true)

	// 存储默认语言的文章数据（用于显示文章列表和操作）
	const [defaultLanguageArticles, setDefaultLanguageArticles] = useState<
		ArticlePost[]
	>([])

	// 记录默认语言的选中文章
	const [selectedArticle, setSelectedArticle] = useState<ArticlePost | null>(null)

	// 记录非默认语言的选中文章
	const [selectedLocalArticle, setSelectedLocalArticle] =
		useState<ArticlePost | null>(null)

	// 标记是否已加载默认语言数据
	const hasLoadedDefaultLanguage = useRef(false)

	// 跟踪上一次请求的语言
	const [lastRequestedLanguage, setLastRequestedLanguage] = useState<
		string | null
	>(null)

	// 获取默认语言的文章数据
	const { mutate: refreshDefaultArticles } = useSWR<any>(
		defaultLanguage
			? `/api/project-articles/page?projectId=${projectId}&locale=${defaultLanguage}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				const defaultArticles = data?.data || []
				setDefaultLanguageArticles(defaultArticles)
				hasLoadedDefaultLanguage.current = true

				// 如果当前语言就是默认语言，直接设置加载完成
				if (currentLanguage === defaultLanguage) {
					setIsLoading(false)
				}
			},
			onError: (error) => {
				console.error("Failed to load default language articles:", error)
				setIsLoading(false)
			},
		},
	)

	// 使用 useSWR 加载当前语言的文章数据（仅当不是默认语言时）
	const { isValidating, mutate: refreshCurrentLanguage } = useSWR<any>(
		currentLanguage &&
			currentLanguage !== defaultLanguage &&
			hasLoadedDefaultLanguage.current
			? `/api/project-articles/page?projectId=${projectId}&locale=${currentLanguage}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				// 请求成功后更新上一次请求的语言
				setLastRequestedLanguage(currentLanguage)

				// 只处理非默认语言的选中文章数据，不影响文章列表显示
				// 文章列表始终显示默认语言的数据
				if (selectedArticle) {
					// 找到当前语言中对应的文章
					const currentArticles = data?.data || []
					const currentArticleMatch = currentArticles.find(
						(article: ArticlePost) => article.id === selectedArticle.id,
					)

					if (currentArticleMatch) {
						setSelectedLocalArticle(currentArticleMatch)
					} else {
						// 如果找不到对应的文章，创建一个基于默认语言的新文章
						const newLocalArticle: ArticlePost = {
							...selectedArticle,
							locale: currentLanguage,
							title: "",
							mdxContent: "",
							metadata: {
								title: "",
								description: "",
							},
						}
						setSelectedLocalArticle(newLocalArticle)
					}
				}

				setIsLoading(false)
			},
			onError: (error) => {
				console.error("Failed to load current language articles:", error)
				toast.error("加载文章失败")
				setIsLoading(false)
			},
		},
	)

	// 当语言变化或验证状态变化时更新加载状态
	useEffect(() => {
		// 当语言变化时设置加载状态
		if (currentLanguage && currentLanguage !== lastRequestedLanguage) {
			setIsLoading(true)

			// 如果切换到默认语言，清除本地文章数据
			if (currentLanguage === defaultLanguage) {
				setSelectedLocalArticle(null)
			}

			// 如果已经加载了默认语言数据，设置为非加载状态
			if (hasLoadedDefaultLanguage.current) {
				setIsLoading(false)
			}
		}
		// 如果不在验证中，且上一次请求的语言与当前语言相同，则设置为非加载状态
		else if (!isValidating && lastRequestedLanguage === currentLanguage) {
			setIsLoading(false)
		}
	}, [currentLanguage, lastRequestedLanguage, defaultLanguage, isValidating])

	// 当默认语言文章数据变化时，处理选中的文章
	useEffect(() => {
		// 如果没有文章数据，不做处理
		if (defaultLanguageArticles.length === 0) return

		// 如果没有选中的文章，自动选择第一个文章
		if (!selectedArticle) {
			const firstArticle = defaultLanguageArticles[0]
			if (firstArticle) {
				setSelectedArticle(firstArticle)
			}
		}
	}, [defaultLanguageArticles, selectedArticle])

	// 使用useSWR获取当前选中文章的特定语言数据
	useSWR<any>(
		selectedArticle && currentLanguage !== defaultLanguage
			? `/api/project-articles?id=${selectedArticle.id}&locale=${currentLanguage}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				if (data && selectedArticle) {
					// 更新非默认语言的选中文章
					setSelectedLocalArticle(data)
				}
			},
			onError: (error) => {
				console.error("Failed to fetch article for language:", error)
				// 出错时，创建一个基于默认语言的新文章
				if (selectedArticle) {
					const newLocalArticle: ArticlePost = {
						...selectedArticle,
						locale: currentLanguage,
						title: "",
						mdxContent: "",
						metadata: {
							title: "",
							description: "",
						},
					}
					setSelectedLocalArticle(newLocalArticle)
				}
			},
		},
	)

	// 当切换回默认语言时，清除本地文章数据
	useEffect(() => {
		if (currentLanguage === defaultLanguage) {
			setSelectedLocalArticle(null)
		}
	}, [currentLanguage, defaultLanguage])

	// 添加新文章
	const addArticle = () => {
		// 生成唯一的文章ID
		let articleId = generateArticleId()
		// 检查ID是否已存在，如果存在则重新生成
		while (
			defaultLanguageArticles.some((article) => article.id === articleId)
		) {
			articleId = generateArticleId()
		}

		const newArticle: ArticlePost = {
			id: articleId,
			slug: `article-${articleId}`,
			title: "新文章",
			locale: defaultLanguage,
			titleImageUrl: "",
			updateTime: new Date().toISOString(),
			mdxContent: "# 新文章\n\n在这里编写文章内容",
			metadata: {
				title: "新文章",
				description: "这是一篇新文章",
				ogTitle: "",
				ogDescription: "",
				ogImage: "",
			},
		}

		// 更新默认语言文章列表
		const updatedArticles = [...defaultLanguageArticles, newArticle]
		setDefaultLanguageArticles(updatedArticles)
		setSelectedArticle(newArticle)

		// 保存到服务器
		saveArticle(newArticle)
	}

	// 更新文章
	const updateArticle = async (updatedArticle: ArticlePost) => {
		// 判断是更新默认语言还是当前语言的文章
		if (currentLanguage === defaultLanguage) {
			// 更新默认语言文章
			const updatedArticles = defaultLanguageArticles.map((article: ArticlePost) =>
				article.id === updatedArticle.id ? updatedArticle : article,
			)

			setDefaultLanguageArticles(updatedArticles)
			setSelectedArticle(updatedArticle)

			// 保存到服务器
			try {
				const success = await saveArticle(updatedArticle)

				if (success) {
					toast.success("文章保存成功")

					// 如果是默认语言，询问是否需要翻译
					if (languages.length > 0) {
						// 打开翻译对话框
						// openTranslationDialog()
					}
				} else {
					toast.error("文章保存失败")
				}
			} catch (error) {
				console.error("Failed to save article:", error)
				toast.error("文章保存失败")
			}
		} else {
			// 更新非默认语言文章
			setSelectedLocalArticle(updatedArticle)

			// 直接保存当前文章到服务器
			try {
				const success = await saveArticle(updatedArticle)

				if (success) {
					toast.success("文章保存成功")
				} else {
					toast.error("文章保存失败")
				}
			} catch (error) {
				console.error("Failed to save article:", error)
				toast.error("文章保存失败")
			}
		}
	}

	// 处理文章排序
	const reorderArticles = async (reorderedArticles: ArticlePost[]) => {
		// 更新默认语言文章
		setDefaultLanguageArticles(reorderedArticles)

		// TODO: 实现保存排序的API调用
		toast.success("文章排序已保存")
	}

	// 删除文章
	const deleteArticle = async (articleId: string) => {
		// 更新本地状态
		const updatedArticles = defaultLanguageArticles.filter(
			(article: ArticlePost) => article.id !== articleId,
		)

		// 更新默认语言文章
		setDefaultLanguageArticles(updatedArticles)

		// 如果删除的是当前选中的文章，清除选中状态
		if (selectedArticle?.id === articleId) {
			setSelectedArticle(null)
		}

		// 保存到服务器
		try {
			const response = await fetch(
				`/api/project-articles?id=${articleId}`,
				{
					method: "DELETE",
				},
			)

			if (response.ok) {
				toast.success("文章删除成功")
			} else {
				// 如果删除失败，恢复原始数据
				toast.error("文章删除失败")
				// 重新加载数据
				refreshDefaultArticles()
			}
		} catch (error) {
			console.error("Failed to delete article:", error)
			toast.error("文章删除失败")
		}
	}

	// 保存文章数据到服务器
	const saveArticle = async (articleToSave: ArticlePost): Promise<boolean> => {
		try {
			// 判断是创建还是更新操作
			const isUpdate = articleToSave.id &&
				defaultLanguageArticles.some(article => article.id === articleToSave.id)

			// 发送请求保存文章数据
			const response = isUpdate
				? await fetchPut(`/api/project-articles`, {
						...articleToSave,
						projectId,
					})
				: await fetchPost(`/api/project-articles`, {
						...articleToSave,
						projectId,
					})

			// 如果API返回了更新后的文章数据，直接使用
			if (response) {
				// 更新本地状态
				if (currentLanguage === defaultLanguage) {
					// 更新默认语言文章列表中的对应文章
					const updatedArticles = defaultLanguageArticles.map((article) =>
						article.id === articleToSave.id ? response : article,
					)
					setDefaultLanguageArticles(updatedArticles)

					// 如果当前有选中的文章，更新选中的文章
					if (selectedArticle && selectedArticle.id === articleToSave.id) {
						setSelectedArticle(response)
					}
				} else if (
					selectedLocalArticle &&
					selectedLocalArticle.id === articleToSave.id
				) {
					// 对于非默认语言，更新选中的本地文章
					setSelectedLocalArticle(response)
				}
			} else {
				// 如果API没有返回文章数据，强制刷新数据
				if (currentLanguage === defaultLanguage) {
					refreshDefaultArticles()
				} else {
					refreshCurrentLanguage()
				}
			}

			return true
		} catch (error) {
			console.error("Error saving article:", error)
			return false
		}
	}

	// 获取当前应该编辑的文章（根据当前语言返回不同的文章对象）
	const getCurrentEditingArticle = (): ArticlePost | null => {
		if (currentLanguage === defaultLanguage) {
			return selectedArticle
		} else {
			// 如果有本地语言文章，返回本地语言文章
			if (selectedLocalArticle) {
				return selectedLocalArticle
			}
			// 如果没有本地语言文章但有默认语言文章，返回一个基于默认语言的新文章
			else if (selectedArticle) {
				return {
					...selectedArticle,
					locale: currentLanguage,
					title: "",
					mdxContent: "",
					metadata: {
						title: "",
						description: "",
					},
				}
			}
			// 如果都没有，返回null
			return null
		}
	}

	// 获取当前编辑的文章
	const currentEditingArticle = getCurrentEditingArticle()

	return {
		isLoading,
		articles: defaultLanguageArticles, // 始终返回默认语言的文章列表
		selectedArticle: currentEditingArticle, // 返回当前应该编辑的文章
		setSelectedArticle, // 保持原有的设置函数名称，以兼容现有代码
		addArticle,
		updateArticle,
		deleteArticle,
		reorderArticles,
	}
}
