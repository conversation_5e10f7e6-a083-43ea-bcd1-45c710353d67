"use client"

import { useState, useEffect } from "react"
import { useParams } from "next/navigation"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { toast } from "sonner"
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Button,
  Badge,
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  Input,
  Textarea,
  Label,
  Switch,
} from "@repo/ui/components"
import { Plus, Edit, Trash2, Upload, TestTube, Eye } from "lucide-react"
import {
  PROMPT_TEMPLATE_TYPE,
  PROMPT_TYPE_LABELS,
  DEFAULT_PROMPT_CONTENTS,
  type PromptTemplateType
} from "@repo/shared-types"

interface PromptTemplate {
  id: string
  projectId: string | null
  type: string
  name: string
  description?: string
  content: string
  isEnabled: boolean
  isDefault: boolean
  isSystemDefault?: boolean // 标记是否为系统默认提示词
  createdAt: string
  updatedAt: string
  creator: {
    id: string
    name: string
    email: string
  }
}

interface PromptFormData {
  id?: string
  type: PromptTemplateType
  name: string
  description: string
  content: string
  isEnabled: boolean
}

export default function PromptsPage() {
  const params = useParams()
  const projectId = params.project_id as string

  const [prompts, setPrompts] = useState<PromptTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [typeFilter, setTypeFilter] = useState<string>("all")

  // 表单状态
  const [showForm, setShowForm] = useState(false)
  const [formData, setFormData] = useState<PromptFormData>({
    type: PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION,
    name: "",
    description: "",
    content: "",
    isEnabled: true
  })
  const [formLoading, setFormLoading] = useState(false)

  // 导入状态
  const [showImportDialog, setShowImportDialog] = useState(false)
  const [sourceProjectId, setSourceProjectId] = useState("")
  const [importLoading, setImportLoading] = useState(false)

  // 测试状态
  const [showTestDialog, setShowTestDialog] = useState(false)
  const [testPromptId, setTestPromptId] = useState("")
  const [testParams, setTestParams] = useState("")
  const [testLoading, setTestLoading] = useState(false)
  const [testResult, setTestResult] = useState<any>(null)

  // 查看详情状态
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [detailPrompt, setDetailPrompt] = useState<PromptTemplate | null>(null)

  // 获取提示词列表
  const fetchPrompts = async (page = 1) => {
    try {
      setLoading(true)
      const params = new URLSearchParams({
        projectId,
        page: page.toString(),
        limit: "20",
        includeSystemDefaults: "true" // 包含系统默认提示词
      })

      if (typeFilter && typeFilter !== "all") params.append("type", typeFilter)

      const response = await fetchGet(`/api/prompts?${params}`)
      setPrompts(response.data)
      setTotalPages(response.pagination.totalPages)
      setCurrentPage(page)
    } catch (error) {
      console.error("获取提示词列表失败:", error)
      toast.error("获取提示词列表失败")
    } finally {
      setLoading(false)
    }
  }

  // 保存提示词
  const savePrompt = async () => {
    try {
      setFormLoading(true)

      const payload = {
        ...formData,
        projectId
      }

      const endpoint = formData.id ? "/api/prompts/update" : "/api/prompts/create"
      await fetchPost(endpoint, payload)
      toast.success(formData.id ? "提示词更新成功" : "提示词创建成功")
      setShowForm(false)
      resetForm()
      fetchPrompts(currentPage)
    } catch (error) {
      console.error("保存提示词失败:", error)
      toast.error("保存提示词失败")
    } finally {
      setFormLoading(false)
    }
  }

  // 删除提示词
  const deletePrompt = async (id: string) => {
    if (!confirm("确定要删除这个提示词吗？")) return

    try {
      await fetchPost("/api/prompts/delete", { id })
      toast.success("提示词删除成功")
      fetchPrompts(currentPage)
    } catch (error) {
      console.error("删除提示词失败:", error)
      toast.error("删除提示词失败")
    }
  }

  // 编辑提示词
  const editPrompt = (prompt: PromptTemplate) => {
    // 如果是系统默认提示词，则基于它创建新的项目提示词
    if (prompt.isSystemDefault) {
      createFromSystemPrompt(prompt)
    } else {
      setFormData({
        id: prompt.id,
        type: prompt.type as PromptTemplateType,
        name: prompt.name,
        description: prompt.description || "",
        content: prompt.content,
        isEnabled: prompt.isEnabled
      })
      setShowForm(true)
    }
  }

  // 基于系统提示词创建项目提示词
  const createFromSystemPrompt = async (systemPrompt: PromptTemplate) => {
    try {
      await fetchPost("/api/prompts/create-from-system", {
        systemPromptId: systemPrompt.id,
        projectId
      })

      toast.success(`已基于系统提示词创建项目级 ${PROMPT_TYPE_LABELS[systemPrompt.type as PromptTemplateType]} 提示词`)
      fetchPrompts(currentPage)
    } catch (error) {
      console.error("基于系统提示词创建失败:", error)
      toast.error("基于系统提示词创建失败")
    }
  }

  // 查看提示词详情
  const viewPromptDetail = (prompt: PromptTemplate) => {
    setDetailPrompt(prompt)
    setShowDetailDialog(true)
  }

  // 重置表单
  const resetForm = () => {
    setFormData({
      type: PROMPT_TEMPLATE_TYPE.GAME_COMMENT_GENERATION,
      name: "",
      description: "",
      content: "",
      isEnabled: true
    })
  }

  // 导入提示词
  const importPrompts = async () => {
    if (!sourceProjectId.trim()) {
      toast.error("请输入源项目ID")
      return
    }

    try {
      setImportLoading(true)
      const response = await fetchPost("/api/prompts/import", {
        sourceProjectId: sourceProjectId.trim(),
        targetProjectId: projectId
      })

      toast.success(`导入完成：成功 ${response.summary.success} 个，失败 ${response.summary.failed} 个`)
      setShowImportDialog(false)
      setSourceProjectId("")
      fetchPrompts(currentPage)
    } catch (error) {
      console.error("导入提示词失败:", error)
      toast.error("导入提示词失败")
    } finally {
      setImportLoading(false)
    }
  }

  // 测试提示词
  const testPrompt = async () => {
    if (!testParams.trim()) {
      toast.error("请输入测试参数")
      return
    }

    try {
      setTestLoading(true)
      const inputParams = JSON.parse(testParams)

      const response = await fetchPost("/api/prompts/test", {
        promptTemplateId: testPromptId,
        projectId,
        inputParams
      })

      setTestResult(response)
      toast.success("测试完成")
    } catch (error) {
      console.error("测试提示词失败:", error)
      toast.error("测试提示词失败")
    } finally {
      setTestLoading(false)
    }
  }

  // 当类型改变时，自动填充默认内容
  const handleTypeChange = (type: PromptTemplateType) => {
    setFormData(prev => ({
      ...prev,
      type,
      content: DEFAULT_PROMPT_CONTENTS[type] || ""
    }))
  }

  // 格式化时间
  const formatTime = (timeString: string) => {
    return new Date(timeString).toLocaleString("zh-CN")
  }

  useEffect(() => {
    fetchPrompts()
  }, [projectId, typeFilter])

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold mb-2">提示词管理</h1>
        <p className="text-muted-foreground">管理项目的AI提示词模板</p>
      </div>

      {/* 操作栏 */}
      <Card className="mb-6">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>提示词列表</CardTitle>
            <div className="flex items-center gap-2">
              <Select value={typeFilter} onValueChange={setTypeFilter}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="类型筛选" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部类型</SelectItem>
                  {Object.entries(PROMPT_TYPE_LABELS).map(([type, label]) => (
                    <SelectItem key={type} value={type}>{label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Dialog open={showImportDialog} onOpenChange={setShowImportDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Upload className="h-4 w-4 mr-1" />
                    导入提示词
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>导入提示词</DialogTitle>
                  </DialogHeader>
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="sourceProjectId">源项目ID</Label>
                      <Input
                        id="sourceProjectId"
                        value={sourceProjectId}
                        onChange={(e) => setSourceProjectId(e.target.value)}
                        placeholder="请输入要导入的项目ID"
                      />
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowImportDialog(false)}>
                        取消
                      </Button>
                      <Button onClick={importPrompts} disabled={importLoading}>
                        {importLoading ? "导入中..." : "导入"}
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>

              <Button onClick={() => setShowForm(true)}>
                <Plus className="h-4 w-4 mr-1" />
                新建提示词
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">加载中...</div>
          ) : prompts.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">暂无提示词</div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>名称</TableHead>
                  <TableHead>类型</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建者</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead>操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {prompts.map((prompt) => (
                  <TableRow key={prompt.id}>
                    <TableCell>
                      <div>
                        <div className="font-medium">{prompt.name}</div>
                        {prompt.description && (
                          <div className="text-sm text-muted-foreground">{prompt.description}</div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="secondary">
                        {PROMPT_TYPE_LABELS[prompt.type as PromptTemplateType] || prompt.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex gap-1">
                        <Badge variant={prompt.isEnabled ? "success" : "secondary"}>
                          {prompt.isEnabled ? "启用" : "禁用"}
                        </Badge>
                        {prompt.isSystemDefault && (
                          <Badge variant="outline">系统默认</Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>{prompt.creator.name || prompt.creator.email}</TableCell>
                    <TableCell>{formatTime(prompt.updatedAt)}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => viewPromptDetail(prompt)}
                          title="查看详情"
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        {!prompt.isSystemDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              setTestPromptId(prompt.id)
                              setShowTestDialog(true)
                            }}
                            title="测试提示词"
                          >
                            <TestTube className="h-4 w-4" />
                          </Button>
                        )}

                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => editPrompt(prompt)}
                          title={prompt.isSystemDefault ? "基于此系统提示词创建项目提示词" : "编辑提示词"}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>

                        {!prompt.isDefault && !prompt.isSystemDefault && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deletePrompt(prompt.id)}
                            title="删除提示词"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}

          {/* 分页 */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-4">
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === 1}
                onClick={() => fetchPrompts(currentPage - 1)}
              >
                上一页
              </Button>
              <span className="text-sm">
                第 {currentPage} 页，共 {totalPages} 页
              </span>
              <Button
                variant="outline"
                size="sm"
                disabled={currentPage === totalPages}
                onClick={() => fetchPrompts(currentPage + 1)}
              >
                下一页
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 提示词表单对话框 */}
      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{formData.id ? "编辑提示词" : "新建提示词"}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="type">提示词类型</Label>
                <Select
                  value={formData.type}
                  onValueChange={handleTypeChange}
                  disabled={!!formData.id}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(PROMPT_TYPE_LABELS).map(([type, label]) => (
                      <SelectItem key={type} value={type}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="name">提示词名称</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="请输入提示词名称"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="description">描述</Label>
              <Input
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="请输入提示词描述（可选）"
              />
            </div>

            <div>
              <Label htmlFor="content">提示词内容</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                placeholder="请输入提示词内容"
                rows={12}
                className="font-mono text-sm"
              />
              <div className="text-xs text-muted-foreground mt-1">
                支持变量：{"{sourceLanguage}"}, {"{targetLanguage}"}, {"{content}"} 等
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="isEnabled"
                checked={formData.isEnabled}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, isEnabled: checked }))}
              />
              <Label htmlFor="isEnabled">启用此提示词</Label>
            </div>

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => setShowForm(false)}>
                取消
              </Button>
              <Button onClick={savePrompt} disabled={formLoading}>
                {formLoading ? "保存中..." : "保存"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 测试对话框 */}
      <Dialog open={showTestDialog} onOpenChange={setShowTestDialog}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>测试提示词</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="testParams">测试参数 (JSON格式)</Label>
              <Textarea
                id="testParams"
                value={testParams}
                onChange={(e) => setTestParams(e.target.value)}
                placeholder='{"sourceLanguage": "zh-CN", "targetLanguage": "en-US", "content": "测试内容"}'
                rows={4}
                className="font-mono text-sm"
              />
            </div>

            {testResult && (
              <div>
                <Label>测试结果</Label>
                <div className="mt-2 p-4 bg-muted rounded-lg">
                  <div className="text-sm space-y-2">
                    <div><strong>状态:</strong> {testResult.result.success ? "成功" : "失败"}</div>
                    <div><strong>耗时:</strong> {testResult.duration}ms</div>
                    <div><strong>剩余测试次数:</strong> {testResult.remainingTests}</div>
                    <div>
                      <strong>结果:</strong>
                      <pre className="mt-1 p-2 bg-background rounded text-xs overflow-auto">
                        {JSON.stringify(testResult.result, null, 2)}
                      </pre>
                    </div>
                  </div>
                </div>
              </div>
            )}

            <div className="flex justify-end gap-2">
              <Button variant="outline" onClick={() => {
                setShowTestDialog(false)
                setTestResult(null)
                setTestParams("")
              }}>
                关闭
              </Button>
              <Button onClick={testPrompt} disabled={testLoading}>
                {testLoading ? "测试中..." : "开始测试"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 查看详情对话框 */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {detailPrompt?.isSystemDefault ? "系统默认提示词详情" : "提示词详情"}
            </DialogTitle>
          </DialogHeader>
          {detailPrompt && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>提示词类型</Label>
                  <div className="mt-1">
                    <Badge variant="secondary">
                      {PROMPT_TYPE_LABELS[detailPrompt.type as PromptTemplateType] || detailPrompt.type}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label>提示词名称</Label>
                  <div className="mt-1 font-medium">{detailPrompt.name}</div>
                </div>
              </div>

              {detailPrompt.description && (
                <div>
                  <Label>描述</Label>
                  <div className="mt-1 text-muted-foreground">{detailPrompt.description}</div>
                </div>
              )}

              <div>
                <Label>提示词内容</Label>
                <div className="mt-1 p-4 bg-muted rounded-lg">
                  <pre className="text-sm whitespace-pre-wrap font-mono overflow-auto">
                    {detailPrompt.content}
                  </pre>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>状态</Label>
                  <div className="mt-1 flex gap-1">
                    <Badge variant={detailPrompt.isEnabled ? "success" : "secondary"}>
                      {detailPrompt.isEnabled ? "启用" : "禁用"}
                    </Badge>
                    {detailPrompt.isSystemDefault && (
                      <Badge variant="outline">系统默认</Badge>
                    )}
                  </div>
                </div>
                <div>
                  <Label>创建者</Label>
                  <div className="mt-1">{detailPrompt.creator.name || detailPrompt.creator.email}</div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>创建时间</Label>
                  <div className="mt-1 text-sm text-muted-foreground">
                    {formatTime(detailPrompt.createdAt)}
                  </div>
                </div>
                <div>
                  <Label>更新时间</Label>
                  <div className="mt-1 text-sm text-muted-foreground">
                    {formatTime(detailPrompt.updatedAt)}
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-2">
                <Button variant="outline" onClick={() => setShowDetailDialog(false)}>
                  关闭
                </Button>
                {detailPrompt.isSystemDefault ? (
                  <Button onClick={() => {
                    setShowDetailDialog(false)
                    createFromSystemPrompt(detailPrompt)
                  }}>
                    基于此提示词创建项目提示词
                  </Button>
                ) : (
                  <Button onClick={() => {
                    setShowDetailDialog(false)
                    editPrompt(detailPrompt)
                  }}>
                    编辑提示词
                  </Button>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}
