"use client"

import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { GameCategory, ProjectLocaleSiteSettingType } from "@repo/shared-types"
import { fetchGet, fetchPost } from "@repo/utils/react"
import { nanoid } from "nanoid"
import { useEffect, useRef, useState } from "react"
import { toast } from "sonner"
import useSWR from "swr"

/**
 * 获取项目站点设置和分类管理的Hook
 */

/**
 * 生成唯一的分类代码
 * @returns 唯一的分类代码字符串
 */
const generateCategoryCode = (): string => {
	// 使用nanoid生成随机字符串
	return `cat_${nanoid(8)}`
}

export function useProjectCategories(
	projectId: string,
	type: ProjectLocaleSiteSettingType = ProjectLocaleSiteSettingType.GameCategories,
) {
	// 使用 LanguageContext 获取语言信息
	const { defaultLanguage, currentLanguage, languages } =
		useTranslationContext()

	// 加载状态
	const [isLoading, setIsLoading] = useState<boolean>(true)

	// 存储默认语言的分类数据（用于显示分类列表和操作）
	const [defaultLanguageCategories, setDefaultLanguageCategories] = useState<
		GameCategory[]
	>([])

	// 记录默认语言的选中分类
	const [selectedCategory, setSelectedCategory] = useState<GameCategory | null>(
		null,
	)

	// 记录非默认语言的选中分类
	const [selectedLocalCategory, setSelectedLocalCategory] =
		useState<GameCategory | null>(null)

	// 标记是否已加载默认语言数据
	const hasLoadedDefaultLanguage = useRef(false)

	// 跟踪上一次请求的语言
	const [lastRequestedLanguage, setLastRequestedLanguage] = useState<
		string | null
	>(null)

	// 获取默认语言的分类数据
	const { mutate: refreshDefaultCategories } = useSWR<any>(
		defaultLanguage
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${defaultLanguage}&type=${type}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				const defaultCategories = data?.content || []
				setDefaultLanguageCategories(defaultCategories)
				hasLoadedDefaultLanguage.current = true

				// 如果当前语言就是默认语言，直接设置加载完成
				if (currentLanguage === defaultLanguage) {
					setIsLoading(false)
				}
			},
			onError: (error) => {
				console.error("Failed to load default language categories:", error)
				setIsLoading(false)
			},
		},
	)

	// 使用 useSWR 加载当前语言的分类数据（仅当不是默认语言时）
	const { isValidating, mutate: refreshCurrentLanguage } = useSWR<any>(
		currentLanguage &&
			currentLanguage !== defaultLanguage &&
			hasLoadedDefaultLanguage.current
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${currentLanguage}&type=${type}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				// 请求成功后更新上一次请求的语言
				setLastRequestedLanguage(currentLanguage)

				// 只处理非默认语言的选中分类数据，不影响分类列表显示
				// 分类列表始终显示默认语言的数据
				if (selectedCategory) {
					// 找到当前语言中对应的分类
					const currentCategories = data?.content || []
					const currentCategoryMatch = currentCategories.find(
						(cat: GameCategory) => cat.code === selectedCategory.code,
					)

					if (currentCategoryMatch) {
						setSelectedLocalCategory(currentCategoryMatch)
					} else {
						// 如果找不到对应的分类，创建一个基于默认语言的新分类
						const newLocalCategory: GameCategory = {
							...selectedCategory,
							locale: currentLanguage,
              name: "",
							metadata: {
								title: "",
								description: "",
							},
						}
						setSelectedLocalCategory(newLocalCategory)
					}
				}

				setIsLoading(false)
			},
			onError: (error) => {
				console.error("Failed to load current language categories:", error)
				toast.error("加载分类失败")
				setIsLoading(false)
			},
		},
	)

	// 当语言变化或验证状态变化时更新加载状态
	useEffect(() => {
		// 当语言变化时设置加载状态
		if (currentLanguage && currentLanguage !== lastRequestedLanguage) {
			setIsLoading(true)

			// 如果切换到默认语言，清除本地分类数据
			if (currentLanguage === defaultLanguage) {
				setSelectedLocalCategory(null)
			}

			// 如果已经加载了默认语言数据，设置为非加载状态
			if (hasLoadedDefaultLanguage.current) {
				setIsLoading(false)
			}
		}
		// 如果不在验证中，且上一次请求的语言与当前语言相同，则设置为非加载状态
		else if (!isValidating && lastRequestedLanguage === currentLanguage) {
			setIsLoading(false)
		}
	}, [currentLanguage, lastRequestedLanguage, defaultLanguage, isValidating])

	// 当默认语言分类数据变化时，处理选中的分类
	useEffect(() => {
		// 如果没有分类数据，不做处理
		if (defaultLanguageCategories.length === 0) return

		// 如果没有选中的分类，自动选择第一个分类
		if (!selectedCategory) {
			// 优先选择顶级分类 - 这里需要根据实际情况判断顶级分类
			// 由于移除了parentId，我们可以根据其他逻辑判断顶级分类
			// 例如，可以假设所有分类都是顶级分类
			const topLevelCategories = defaultLanguageCategories
			if (topLevelCategories.length > 0) {
				// 确保类型安全
				const firstCategory = topLevelCategories[0]
				if (firstCategory) {
					setSelectedCategory(firstCategory)
				}
			} else if (defaultLanguageCategories[0]) {
				// 如果没有顶级分类，选择第一个分类（确保类型安全）
				setSelectedCategory(defaultLanguageCategories[0])
			}
		}
	}, [defaultLanguageCategories, selectedCategory])

	// 使用useSWR获取当前选中分类的特定语言数据
	useSWR<any>(
		selectedCategory && currentLanguage !== defaultLanguage
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${currentLanguage}&type=${type}&categoryCode=${selectedCategory.code}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				if (data?.content && selectedCategory) {
					// 找到对应code的分类
					const categoryInCurrentLanguage = data.content.find(
						(cat: GameCategory) => cat.code === selectedCategory.code,
					)

					if (categoryInCurrentLanguage) {
						// 更新非默认语言的选中分类
						setSelectedLocalCategory(categoryInCurrentLanguage)
					} else {
						// 如果找不到对应的分类，创建一个基于默认语言的新分类
						const newLocalCategory: GameCategory = {
							...selectedCategory,
							name: "",
							locale: currentLanguage,
							metadata: {
								title: "",
								description: "",
							},
						}
						setSelectedLocalCategory(newLocalCategory)
					}
				}
			},
			onError: (error) => {
				console.error("Failed to fetch category for language:", error)
				// 出错时，创建一个基于默认语言的新分类
				if (selectedCategory) {
					const newLocalCategory: GameCategory = {
						...selectedCategory,
						name: "",
						locale: currentLanguage,
						metadata: {
							title: "",
							description: "",
						},
					}
					setSelectedLocalCategory(newLocalCategory)
				}
			},
		},
	)

	// 当切换回默认语言时，清除本地分类数据
	useEffect(() => {
		if (currentLanguage === defaultLanguage) {
			setSelectedLocalCategory(null)
		}
	}, [currentLanguage, defaultLanguage])

	// 添加新分类
	const addCategory = () => {
		// 获取分类数量，用于生成默认名称
		const topLevelCount = defaultLanguageCategories.length + 1

		// 生成唯一的分类代码
		let categoryCode = generateCategoryCode()
		// 检查代码是否已存在，如果存在则重新生成
		while (
			defaultLanguageCategories.some(
				(cat: GameCategory) => cat.code === categoryCode,
			)
		) {
			categoryCode = generateCategoryCode()
		}

		const newCategory: GameCategory = {
			code: categoryCode,
			name: `未命名分类-${topLevelCount}`,
			icon: "gamepad",
			sortOrder: defaultLanguageCategories.length + 1,
			slug: "",
			locale: defaultLanguage,
			count: 0,
			children: [],
			metadata: {
				title: "",
				description: "",
			},
		}

		// 更新默认语言分类列表
		const updatedCategories = [...defaultLanguageCategories, newCategory]
		setDefaultLanguageCategories(updatedCategories)
		setSelectedCategory(newCategory)

		// 保存到服务器
		saveCategories(updatedCategories)
	}

	// 添加子分类
	const addSubCategory = (parentCode: string, parentNumber: string) => {
		// 获取父分类
		const parentCategory = defaultLanguageCategories.find(
			(cat: GameCategory) => cat.code === parentCode,
		)
		if (!parentCategory) return

		// 由于移除了parentId，我们需要使用其他方式来确定子分类
		// 这里简单地使用序号
		const siblingCount = 1

		// 生成默认名称，格式为 "父级名称-序号"
		const defaultName = `${parentNumber}-${siblingCount}`

		// 生成唯一的分类代码
		let childCode = generateCategoryCode()
		// 检查代码是否已存在，如果存在则重新生成
		while (
			defaultLanguageCategories.some(
				(cat: GameCategory) => cat.code === childCode,
			)
		) {
			childCode = generateCategoryCode()
		}

		const newCategory: GameCategory = {
			code: childCode,
			name: defaultName,
			icon: "gamepad",
			sortOrder: siblingCount,
			slug: "",
			locale: defaultLanguage,
			metadata: {
				title: "",
				description: "",
			},
		}

		// 创建一个新的分类数组
		const updatedCategories = [...defaultLanguageCategories, newCategory]
		setDefaultLanguageCategories(updatedCategories)
		setSelectedCategory(newCategory)

		// 保存到服务器
		saveCategories(updatedCategories)
	}

	// 更新分类
	const updateCategory = async (updatedCategory: GameCategory, options?: { isTranslation?: boolean }) => {
		// 判断是更新默认语言还是当前语言的分类
		if (currentLanguage === defaultLanguage) {
			// 更新默认语言分类
			const updatedCategories = defaultLanguageCategories.map(
				(cat: GameCategory) =>
					cat.code === updatedCategory.code ? updatedCategory : cat,
			)

			setDefaultLanguageCategories(updatedCategories)
			setSelectedCategory(updatedCategory)

			// 保存到服务器
			try {
				const success = await saveCategories(updatedCategories, options?.isTranslation)

				if (success) {
					toast.success("分类保存成功")
				} else {
					toast.error("分类保存失败")
				}
			} catch (error) {
				console.error("Failed to save categories:", error)
				toast.error("分类保存失败")
			}
		} else {
			// 更新非默认语言分类
			setSelectedLocalCategory(updatedCategory)

			// 直接保存当前分类到服务器
			try {
				// 发送请求保存单个分类数据
				const response = await fetchPost(`/api/project-locale-site-settings`, {
					projectId,
					locale: currentLanguage,
					type,
					content: [updatedCategory], // 只保存当前编辑的分类
					isTranslation: options?.isTranslation,
				})

				if (response) {
					toast.success("分类保存成功")
				} else {
					toast.error("分类保存失败")
				}
			} catch (error) {
				console.error("Failed to save category:", error)
				toast.error("分类保存失败")
			}
		}
	}

	// 处理分类排序
	const reorderCategories = async (reorderedCategories: GameCategory[]) => {
		// 更新默认语言分类
		setDefaultLanguageCategories(reorderedCategories)

		// 保存到服务器
		try {
			const success = await saveCategories(reorderedCategories)

			if (success) {
				toast.success("分类排序已保存")
			} else {
				toast.error("分类排序保存失败")
			}
		} catch (error) {
			console.error("Failed to save category order:", error)
			toast.error("分类排序保存失败")
		}
	}

	// 删除分类
	const deleteCategory = async (categoryCode: string) => {
		// 同时删除该分类及其所有子分类
		const updatedCategories = defaultLanguageCategories.filter(
			(cat: GameCategory) => {
				// 排除当前分类
				if (cat.code === categoryCode) return false
				// 排除所有子分类 - 这里需要检查是否有父级关系
				return true
			},
		)

		// 更新默认语言分类
		setDefaultLanguageCategories(updatedCategories)

		// 如果删除的是当前选中的分类，清除选中状态
		if (selectedCategory?.code === categoryCode) {
			setSelectedCategory(null)
		}

		// 保存到服务器
		try {
			const success = await saveCategories(updatedCategories)

			if (success) {
				toast.success("分类删除成功")
			} else {
				// 如果保存失败，恢复原始数据
				toast.error("分类删除失败")
				// 重新加载数据
				refreshDefaultCategories()
			}
		} catch (error) {
			console.error("Failed to save categories after deletion:", error)
			toast.error("分类删除失败")
		}
	}

	// 保存分类数据到服务器
	const saveCategories = async (
		categoriesToSave: GameCategory[],
		isTranslation?: boolean,
	): Promise<boolean> => {
		try {
			// 发送请求保存分类数据
			const response = await fetchPost(`/api/project-locale-site-settings`, {
				projectId,
				locale: currentLanguage,
				type,
				content: categoriesToSave,
				isTranslation,
			})

			// 如果API返回了更新后的分类数据，直接使用
			if (response.content) {
				// 更新本地状态
				if (currentLanguage === defaultLanguage) {
					setDefaultLanguageCategories(response.content)

					// 如果当前有选中的分类，更新选中的分类
					if (selectedCategory) {
						const updatedSelectedCategory = response.content.find(
							(cat: GameCategory) => cat.code === selectedCategory.code,
						)
						if (updatedSelectedCategory) {
							setSelectedCategory(updatedSelectedCategory)
						}
					}
				} else if (selectedLocalCategory) {
					// 对于非默认语言，更新选中的本地分类
					const updatedLocalCategory = response.content.find(
						(cat: GameCategory) => cat.code === selectedLocalCategory.code,
					)
					if (updatedLocalCategory) {
						setSelectedLocalCategory(updatedLocalCategory)
					}
				}
			} else {
				// 如果API没有返回分类数据，强制刷新数据
				if (currentLanguage === defaultLanguage) {
					refreshDefaultCategories()
				} else {
					refreshCurrentLanguage()
				}
			}

			return true
		} catch (error) {
			console.error("Error saving categories:", error)
			return false
		}
	}

	// 获取当前应该编辑的分类（根据当前语言返回不同的分类对象）
	const getCurrentEditingCategory = (): GameCategory | null => {
		if (currentLanguage === defaultLanguage) {
			return selectedCategory
		} else {
			// 如果有本地语言分类，返回本地语言分类
			if (selectedLocalCategory) {
				return selectedLocalCategory
			}
			// 如果没有本地语言分类但有默认语言分类，返回一个基于默认语言的新分类
			else if (selectedCategory) {
				return {
					...selectedCategory,
					locale: currentLanguage,
					name: "",
					metadata: {
						title: "",
						description: "",
					},
				}
			}
			// 如果都没有，返回null
			return null
		}
	}

	// 获取当前编辑的分类
	const currentEditingCategory = getCurrentEditingCategory()

	return {
		isLoading,
		categories: defaultLanguageCategories, // 始终返回默认语言的分类列表
		selectedCategory: currentEditingCategory, // 返回当前应该编辑的分类
		setSelectedCategory, // 保持原有的设置函数名称，以兼容现有代码
		addCategory,
		addSubCategory,
		updateCategory,
		deleteCategory,
		reorderCategories,
	}
}
