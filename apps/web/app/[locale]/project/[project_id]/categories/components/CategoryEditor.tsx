"use client"

import { IconSelector } from "@/lib/components/icons/IconSelector"
import { ICON_GROUPS } from "@/lib/components/icons/icon-helpers"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { useFormWithTranslation, TranslationConfirmDialog } from "@/lib/components/translation"
import { zodResolver } from "@hookform/resolvers/zod"
import { GameCategory } from "@repo/shared-types"
import {
	Button,
	Form,
	FormControl,
	FormDescription,
	FormField,
	FormItem,
	FormLabel,
	Input,
	Textarea,
} from "@repo/ui/components"
import { useForm } from "react-hook-form"
import { useEffect } from "react"
import { z } from "zod"

interface CategoryEditorProps {
	category: GameCategory
	onSave: (category: GameCategory, options?: { isTranslation?: boolean }) => void
	isArticleCategory?: boolean
	projectId?: string // 项目ID，用于上传图标
	isLoading?: boolean
}

// 定义表单验证模式
const formSchema = z.object({
	name: z.string().min(1, { message: "分类名称不能为空" }),
	icon: z.string(),
	slug: z.string(),
	sortOrder: z.number().int().min(0),
	metadata: z.object({
		title: z.string(),
		description: z.string(),
	}),
})

export default function CategoryEditor({
	category,
	onSave,
	isArticleCategory = false,
	projectId: resolvedProjectId,
	isLoading = false,
}: CategoryEditorProps) {
	// 获取语言上下文
	const { currentLanguage, defaultLanguage } = useTranslationContext()

	// 判断当前是否为默认语言
	const isDefaultLanguage = currentLanguage === defaultLanguage

	// 集成翻译确认功能
	const { handleSubmit: handleSubmitWithTranslation, translationDialogProps } = useFormWithTranslation(
		async (data: GameCategory, options?: { isTranslation?: boolean }) => {
			return onSave(data, options)
		},
		{
			projectId: resolvedProjectId || "",
			contentType: isArticleCategory ? "article-categories" : "game-categories",
			confirmOptions: {
				title: "发起分类翻译任务",
				description: "是否同时将分类信息翻译到其他语言？",
				confirmText: "发起翻译",
				cancelText: "仅保存",
			}
		}
	)

	// 定义表单
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: category.name,
			icon: category.icon,
			sortOrder: category.sortOrder,
			slug: category.slug,
			metadata: {
				title: category.metadata?.title,
				description: category.metadata?.description,
			},
		},
	})

	// 当分类或语言变化时重置表单
	useEffect(() => {
		form.reset({
			name: category.name,
			icon: category.icon,
			sortOrder: category.sortOrder,
			slug: category.slug,
			metadata: {
				title: category.metadata?.title,
				description: category.metadata?.description,
			},
		})
	}, [category, currentLanguage, form])

	// 监听名称和meta标题的变化
	const name = form.watch("name")
	const metadataTitle = form.watch("metadata.title")

	// 当名称变化且meta标题为空时，自动设置meta标题为名称
	useEffect(() => {
		if (name && (!metadataTitle || metadataTitle.trim() === "")) {
			form.setValue("metadata.title", name)
		}
	}, [name, metadataTitle, form])

	// 处理表单提交
	async function onSubmit(values: z.infer<typeof formSchema>) {
		// 如果名称为空，设置一个默认值
		if (!values.name.trim()) {
			values.name = isArticleCategory ? "新文章分类" : "新游戏分类"
		}

		// 如果路径为空，根据名称生成一个路径
		if (!values.slug?.trim()) {
			const path = values.name
				.trim()
				.toLowerCase()
				.replace(/\s+/g, "-")
				.replace(/[^\w\-]+/g, "")

			values.slug = path
		}

		// 如果Meta标题为空，使用分类名称作为Meta标题
		if (!values.metadata.title?.trim()) {
			values.metadata.title = values.name
		}

		// 构建更新后的分类对象
		const updatedCategory: GameCategory = {
			code: category.code,
			count: category.count,
			name: values.name,
			icon: values.icon,
			sortOrder: values.sortOrder,
			slug: values.slug,
			locale: currentLanguage,
			metadata: {
				title: values.metadata.title,
				description: values.metadata.description,
			},
		}

		// 使用翻译确认功能提交
		await handleSubmitWithTranslation(updatedCategory)
	}

	function handleReset() {
		form.reset(category)
	}

	return (
		<>
			<Form {...form}>
				<form
					onSubmit={form.handleSubmit(onSubmit)}
					onReset={handleReset}
					className="space-y-6"
				>
				{/* 基本信息 */}
				<div className="space-y-4">
					<div className="grid md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="name"
							render={({ field }) => (
								<FormItem>
									<FormLabel>分类名称</FormLabel>
									<FormControl>
										<Input placeholder="输入分类名称" {...field} />
									</FormControl>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="icon"
							render={({ field }) => (
								<FormItem>
									<FormLabel>分类图标</FormLabel>
									<FormControl>
										<IconSelector
											value={field.value}
											onChange={isDefaultLanguage ? field.onChange : undefined}
											groups={ICON_GROUPS}
											variant="dialog"
											allowUpload={isDefaultLanguage}
											projectId={resolvedProjectId}
											className={
												!isDefaultLanguage
													? "opacity-70 pointer-events-none"
													: ""
											}
										/>
									</FormControl>
								</FormItem>
							)}
						/>
					</div>

					<div className="grid md:grid-cols-2 gap-6">
						<FormField
							control={form.control}
							name="sortOrder"
							render={({ field }) => (
								<FormItem>
									<FormLabel>排序</FormLabel>
									<FormControl>
										<Input
											type="number"
											{...field}
											disabled={!isDefaultLanguage}
											onChange={(e) =>
												field.onChange(Number(e.target.value) || 0)
											}
											value={field.value?.toString() ?? "0"}
										/>
									</FormControl>
									<FormDescription>
										{!isDefaultLanguage
											? "非默认语言下不可编辑"
											: "数字越大排序越靠前"}
									</FormDescription>
								</FormItem>
							)}
						/>

						<FormField
							control={form.control}
							name="slug"
							render={({ field }) => (
								<FormItem>
									<FormLabel>URL路径</FormLabel>
									<FormControl>
										<Input
											{...field}
											disabled={!isDefaultLanguage}
											placeholder="分类URL路径"
										/>
									</FormControl>
									<FormDescription>
										{!isDefaultLanguage
											? "非默认语言下不可编辑"
											: "用于生成分类页面URL"}
									</FormDescription>
								</FormItem>
							)}
						/>
					</div>
				</div>

				{/* SEO设置 */}
				<div className="border-t pt-6">
					<h3 className="text-base font-medium mb-4">SEO设置</h3>
					<div className="space-y-4">
						<div className="grid gap-6">
							<FormField
								control={form.control}
								name="metadata.title"
								render={({ field }) => (
									<FormItem>
										<FormLabel>Meta标题</FormLabel>
										<FormControl>
											<Input placeholder="输入Meta标题" {...field} />
										</FormControl>
									</FormItem>
								)}
							/>
						</div>

						<FormField
							control={form.control}
							name="metadata.description"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Meta描述</FormLabel>
									<FormControl>
										<Textarea placeholder="输入Meta描述" {...field} />
									</FormControl>
								</FormItem>
							)}
						/>
					</div>
				</div>

				{/* 底部固定按钮 */}
				<div className="fixed bottom-0 left-0 right-0 bg-background/80 backdrop-blur-sm border-t border-border py-3 sm:py-4 px-4 sm:px-6 z-10 shadow-lg">
					<div className="max-w-6xl mx-auto flex justify-end w-full">
						<Button
							type="reset"
							variant="outline"
							className="mr-3"
							loading={isLoading}
						>
							重置
						</Button>
						<Button type="submit" loading={isLoading}>
							保存设置
						</Button>
					</div>
				</div>
			</form>
		</Form>

		{/* 翻译确认对话框 */}
		<TranslationConfirmDialog {...translationDialogProps} />
	</>
	)
}
