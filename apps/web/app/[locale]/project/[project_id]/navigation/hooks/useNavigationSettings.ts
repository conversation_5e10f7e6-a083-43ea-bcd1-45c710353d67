"use client"

import { useState, useEffect, useCallback, useRef } from "react"
import { toast } from "sonner"
import { NavItem } from "../types"
import { useTranslationContext } from "@/lib/components/translation/TranslationContext"
import { fetchGet, fetchPost } from "@repo/utils/react"
import useS<PERSON> from "swr"
import { nanoid } from "nanoid"
import { ProjectLocaleSiteSettingType } from "@repo/shared-types"

export enum NavigationType {
	HEADER = "Nav", // 使用 "main" 而不是 "header"，以保持与 API 端的一致性
	FOOTER = "Footer",
}

/**
 * 生成菜单项ID
 * @returns 唯一的菜单项ID字符串
 */
const generateMenuId = (): string => {
	// 使用nanoid生成随机字符串
	return `menu_${nanoid(8)}`
}

interface UseNavigationSettingsResult {
	isLoading: boolean
	menuItems: NavItem[]
	selectedMenuItem: NavItem | null
	setSelectedMenuItem: (item: NavItem | null) => void
	addMenuItem: () => void
	addSubMenuItem: (parentId: string) => void
	updateMenuItem: (updatedItem: NavItem) => Promise<void>
	deleteMenuItem: (itemId: string) => Promise<void>
	reorderMenuItems: (reorderedItems: NavItem[]) => Promise<void>
}

export function useNavigationSettings(
	projectId: string,
	type: NavigationType = NavigationType.HEADER,
): UseNavigationSettingsResult {
	// 使用 LanguageContext 获取语言信息
	const { defaultLanguage, currentLanguage, languages } =
		useTranslationContext()

	// 加载状态
	const [isLoading, setIsLoading] = useState<boolean>(true)

	// 存储默认语言的菜单数据（用于显示菜单列表和操作）
	const [defaultLanguageMenuItems, setDefaultLanguageMenuItems] = useState<
		NavItem[]
	>([])

	// 记录默认语言的选中菜单项
	const [selectedMenuItem, setSelectedMenuItem] = useState<NavItem | null>(null)

	// 记录非默认语言的选中菜单项
	const [selectedLocalMenuItem, setSelectedLocalMenuItem] =
		useState<NavItem | null>(null)

	// 跟踪上一次请求的语言
	const [lastRequestedLanguage, setLastRequestedLanguage] = useState<
		string | null
	>(null)

	// 标记是否已加载默认语言数据
	const hasLoadedDefaultLanguage = useRef(false)

	// 移除合并逻辑，在新增时添加默认值

	// 获取默认语言的菜单数据
	const { mutate: refreshDefaultMenuItems } = useSWR<any>(
		defaultLanguage
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${defaultLanguage}&type=${ProjectLocaleSiteSettingType.Nav}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				// 确保数据是数组格式
				const defaultItems = Array.isArray(data?.content)
					? data.content
					: data?.content?.items || []

				setDefaultLanguageMenuItems(defaultItems)
				hasLoadedDefaultLanguage.current = true

				// 如果当前语言就是默认语言，直接使用默认语言数据
				if (currentLanguage === defaultLanguage) {
					setIsLoading(false)
				}
			},
			onError: (error) => {
				console.error("Failed to load default language menu items:", error)
				setIsLoading(false)
			},
		},
	)

	/**
	 * 递归查找菜单项
	 * @param items 菜单项数组
	 * @param id 要查找的菜单项ID
	 * @returns 找到的菜单项或undefined
	 */
	const findMenuItemById = (
		items: NavItem[],
		id: string,
	): NavItem | undefined => {
		for (const item of items) {
			if (item.id === id) {
				return item
			}

			// 递归查找子菜单
			if (item.children && item.children.length > 0) {
				const found = findMenuItemById(item.children, id)
				if (found) {
					return found
				}
			}
		}
		return undefined
	}

	// 使用 useSWR 加载当前语言的菜单数据（仅当不是默认语言时）
	const { isValidating, mutate: refreshCurrentLanguage } = useSWR<any>(
		currentLanguage &&
			currentLanguage !== defaultLanguage &&
			hasLoadedDefaultLanguage.current
			? `/api/project-locale-site-settings?projectId=${projectId}&locale=${currentLanguage}&type=${ProjectLocaleSiteSettingType.Nav}`
			: null,
		fetchGet,
		{
			revalidateOnFocus: false,
			revalidateOnReconnect: false,
			onSuccess: (data) => {
				// 请求成功后更新上一次请求的语言
				setLastRequestedLanguage(currentLanguage)

				// 确保数据是数组格式
				const currentItems = Array.isArray(data?.content)
					? data.content
					: data?.content?.items || []

				// 更新菜单项状态
				setIsLoading(false)

				// 如果有选中的菜单项，更新对应的本地语言菜单项
				if (selectedMenuItem) {
					// 递归查找选中菜单项在当前语言数据中的对应项
					const localMenuItem = findMenuItemById(
						currentItems,
						selectedMenuItem.id,
					)

					if (localMenuItem) {
						// 更新非默认语言的选中菜单项
						setSelectedLocalMenuItem(localMenuItem)
					} else {
						// 如果找不到对应的菜单项，创建一个基于默认语言的新菜单项
						const newLocalMenuItem: NavItem = {
							...selectedMenuItem,
							label: "",
						}
						setSelectedLocalMenuItem(newLocalMenuItem)
					}
				}
			},
			onError: (error) => {
				console.error("Failed to load current language menu items:", error)
				// 如果当前语言加载失败，但已有默认语言数据，不需要额外处理
				// 因为我们始终使用 defaultLanguageMenuItems 作为菜单列表
				toast.error("加载菜单失败")
				setIsLoading(false)
			},
		},
	)

	// 当语言变化或验证状态变化时更新加载状态
	useEffect(() => {
		// 当语言变化时设置加载状态
		if (currentLanguage && currentLanguage !== lastRequestedLanguage) {
			setIsLoading(true)

			// 如果切换到默认语言，清除本地菜单项数据
			if (currentLanguage === defaultLanguage) {
				setSelectedLocalMenuItem(null)
			}

			// 如果切换到默认语言，直接使用已加载的默认语言数据
			if (
				currentLanguage === defaultLanguage &&
				hasLoadedDefaultLanguage.current
			) {
				// 不需要额外设置菜单项，因为我们始终使用 defaultLanguageMenuItems
				setIsLoading(false)
			}
		}
		// 如果不在验证中，且上一次请求的语言与当前语言相同，则设置为非加载状态
		else if (!isValidating && lastRequestedLanguage === currentLanguage) {
			setIsLoading(false)
		}
	}, [
		currentLanguage,
		lastRequestedLanguage,
		defaultLanguage,
		defaultLanguageMenuItems,
		isValidating,
	])

	// 当菜单数据变化时，处理选中的菜单项
	useEffect(() => {
		// 确保 defaultLanguageMenuItems 是数组
		if (
			!Array.isArray(defaultLanguageMenuItems) ||
			defaultLanguageMenuItems.length === 0
		) {
			return
		}

		// 如果没有选中的菜单项，自动选择第一个菜单项
		if (!selectedMenuItem) {
			// 优先选择顶级菜单项
			const topLevelItems = defaultLanguageMenuItems.filter(
				(item) => item?.parentId === undefined || item?.parentId === null,
			)
			if (topLevelItems.length > 0 && topLevelItems[0]) {
				setSelectedMenuItem(topLevelItems[0])
			} else if (defaultLanguageMenuItems[0]) {
				setSelectedMenuItem(defaultLanguageMenuItems[0])
			}
		}
		// 如果已有选中的菜单项，且语言已切换，则更新选中的菜单项为新语言下的对应菜单项
		else if (selectedMenuItem && lastRequestedLanguage === currentLanguage) {
			// 递归查找选中菜单项在新语言的菜单列表中的对应项
			const updatedMenuItem = findMenuItemById(
				defaultLanguageMenuItems,
				selectedMenuItem.id,
			)

			if (updatedMenuItem) {
				// 更新选中的菜单项为新语言下的对应菜单项
				setSelectedMenuItem(updatedMenuItem)
			}
		}
	}, [
		defaultLanguageMenuItems,
		selectedMenuItem,
		currentLanguage,
		lastRequestedLanguage,
	])

	// 添加菜单项
	const addMenuItem = useCallback(() => {
		// 获取顶级菜单项数量，用于生成默认名称
		const topLevelCount =
			defaultLanguageMenuItems.filter(
				(item) => !item.children || item.children.length === 0,
			).length + 1

		// 生成菜单ID，确保在项目中唯一
		let menuId = generateMenuId()
		// 检查ID是否已存在，如果存在则重新生成
		while (defaultLanguageMenuItems.some((item) => item.id === menuId)) {
			menuId = generateMenuId()
		}

		const newItem: NavItem = {
			id: menuId,
			label: `未命名菜单-${topLevelCount}`,
			href: "/",
			target: "_self",
			children: [],
		}

		// 将新菜单项放在数组的最前面，视觉上更直观
		const newItems = [newItem, ...defaultLanguageMenuItems]

		// 更新本地状态
		setDefaultLanguageMenuItems(newItems)
		setSelectedMenuItem(newItem)

		// 保存到服务器
		saveMenuItems(newItems)
	}, [defaultLanguageMenuItems])

	// 添加子菜单项
	const addSubMenuItem = useCallback(
		(parentId: string) => {
			// 查找父菜单项
			const parentItem = defaultLanguageMenuItems.find(
				(item) => item.id === parentId,
			)

			if (!parentItem) {
				console.error("父菜单项不存在")
				return
			}

			// 获取父菜单项的标签或序号
			const parentLabel = parentItem.label?.split("-")[0] || parentId

			// 获取子菜单项数量，用于命名
			const childrenCount = parentItem.children?.length || 0

			// 生成默认名称，格式为 "父级名称-序号"
			const defaultName = `${parentLabel}-${childrenCount + 1}`

			// 生成子菜单项ID
			let childId = `${parentId}_${generateMenuId()}`

			// 检查ID是否已存在，如果存在则重新生成
			while (defaultLanguageMenuItems.some((item) => item.id === childId)) {
				childId = `${parentId}_${generateMenuId()}`
			}

			const newItem: NavItem = {
				id: childId,
				label: defaultName,
				href: "/",
				target: "_self",
				children: [],
			}

			// 创建新的菜单项数组，将子菜单添加到父菜单的children数组中
			const newItems = defaultLanguageMenuItems.map((item) => {
				if (item.id === parentId) {
					return {
						...item,
						children: [newItem, ...(item.children || [])],
					}
				}
				return item
			})

			// 更新本地状态
			setDefaultLanguageMenuItems(newItems)
			setSelectedMenuItem(newItem)

			// 保存到服务器
			saveMenuItems(newItems)
		},
		[defaultLanguageMenuItems],
	)

	// 更新菜单项
	const updateMenuItem = useCallback(
		async (updatedItem: NavItem) => {
			try {
				// 判断是更新默认语言还是当前语言的菜单项
				if (currentLanguage === defaultLanguage) {
					// 更新默认语言菜单项
					// 需要递归查找并更新菜单项，因为它可能在任何层级
					const updateItemInTree = (items: NavItem[]): NavItem[] => {
						return items.map((item) => {
							if (item.id === updatedItem.id) {
								return updatedItem
							}
							if (item.children && item.children.length > 0) {
								return {
									...item,
									children: updateItemInTree(item.children),
								}
							}
							return item
						})
					}

					const updatedItems = updateItemInTree(defaultLanguageMenuItems)

					setDefaultLanguageMenuItems(updatedItems)
					setSelectedMenuItem(updatedItem)

					// 保存到服务器
					await saveMenuItems(updatedItems)

					
				} else {
					setSelectedLocalMenuItem(updatedItem)

					try {
						// 发送请求保存单个菜单项数据
						const response = await fetchPost(
							`/api/project-locale-site-settings`,
							{
								projectId,
								locale: currentLanguage,
								type: ProjectLocaleSiteSettingType.Nav,
								content: updatedItem,
							},
						)

						if (response) {
							toast.success("菜单项保存成功")
						} else {
							toast.error("菜单项保存失败")
						}
					} catch (error) {
						console.error("Failed to save menu item:", error)
						toast.error("菜单项保存失败")
						return Promise.reject(error)
					}
				}

				return Promise.resolve()
			} catch (error) {
				console.error("Failed to update menu item:", error)
				toast.error("更新菜单项失败，请稍后重试")
				return Promise.reject(error)
			}
		},
		[
			currentLanguage,
			defaultLanguage,
			defaultLanguageMenuItems,
			languages,
			projectId,
			type,
		],
	)

	// 删除菜单项
	const deleteMenuItem = useCallback(
		async (itemId: string) => {
			try {
				// 递归删除菜单项及其子项
				const removeItemFromTree = (items: NavItem[]): NavItem[] => {
					// 过滤掉要删除的项
					return items.filter((item) => {
						if (item.id === itemId) {
							return false // 删除匹配的项
						}

						// 如果有子项，递归处理子项
						if (item.children && item.children.length > 0) {
							item.children = removeItemFromTree(item.children)
						}

						return true
					})
				}

				const newItems = removeItemFromTree(defaultLanguageMenuItems)

				// 更新默认语言菜单
				setDefaultLanguageMenuItems(newItems)

				// 如果当前选中的项被删除，则取消选中
				const isItemDeleted = (id: string, items: NavItem[]): boolean => {
					// 检查当前层级
					if (items.some((item) => item.id === id)) {
						return false // 项目存在，未被删除
					}

					// 检查子层级
					for (const item of items) {
						if (item.children && item.children.length > 0) {
							if (!isItemDeleted(id, item.children)) {
								return false // 在子层级中找到了，未被删除
							}
						}
					}

					return true // 项目不存在，已被删除
				}

				if (selectedMenuItem && isItemDeleted(selectedMenuItem.id, newItems)) {
					setSelectedMenuItem(null)
				}

				// 保存到服务器
				await saveMenuItems(newItems)

				return Promise.resolve()
			} catch (error) {
				console.error("Failed to delete menu item:", error)
				toast.error("删除菜单项失败，请稍后重试")
				return Promise.reject(error)
			}
		},
		[
			defaultLanguageMenuItems,
			selectedMenuItem,
			currentLanguage,
			defaultLanguage,
		],
	)

	// 重新排序菜单项
	const reorderMenuItems = useCallback(
		async (reorderedItems: NavItem[]) => {
			try {
				// 更新默认语言菜单
				setDefaultLanguageMenuItems(reorderedItems)

				// 保存到服务器
				await saveMenuItems(reorderedItems)

				return Promise.resolve()
			} catch (error) {
				console.error("Failed to reorder menu items:", error)
				toast.error("重新排序菜单项失败，请稍后重试")
				return Promise.reject(error)
			}
		},
		[currentLanguage, defaultLanguage],
	)

	// 保存菜单项到服务器
	const saveMenuItems = useCallback(
		async (items: NavItem[]) => {
			try {
				const response = await fetchPost(`/api/project-locale-site-settings`, {
					projectId,
					locale: currentLanguage,
					type: ProjectLocaleSiteSettingType.Nav,
					content: items,
				})

				// 如果API返回了更新后的菜单数据，直接使用
				if (response.content) {
					// 确保数据是数组格式
					const responseItems = response.content

					// 更新本地状态
					if (currentLanguage === defaultLanguage) {
						// 更新默认语言菜单
						setDefaultLanguageMenuItems(responseItems)
					}

					// 如果当前有选中的菜单项，更新选中的菜单项
					if (selectedMenuItem) {
						// 使用我们已经定义的递归查找函数
						const updatedSelectedItem = findMenuItemById(
							responseItems,
							selectedMenuItem.id,
						)
						if (updatedSelectedItem) {
							setSelectedMenuItem(updatedSelectedItem)
						}
					}
				} else {
					// 如果API没有返回菜单数据，强制刷新数据
					if (currentLanguage === defaultLanguage) {
						refreshDefaultMenuItems()
					} else {
						refreshCurrentLanguage()
					}
				}

				const successDescription =
					type === NavigationType.HEADER
						? "头部导航菜单已更新"
						: "页脚设置已更新"

				toast.success("保存成功", {
					description: successDescription,
				})
				return true
			} catch (error) {
				console.error("Failed to save menu:", error)
				toast.error("保存失败", {
					description: "保存导航菜单时出现错误",
				})
				return false
			}
		},
		[
			projectId,
			type,
			currentLanguage,
			defaultLanguage,
			defaultLanguageMenuItems,
			selectedMenuItem,
			refreshDefaultMenuItems,
			refreshCurrentLanguage,
		],
	)

	// 获取当前应该编辑的菜单项（根据当前语言返回不同的菜单对象）
	const getCurrentEditingMenuItem = (): NavItem | null => {
		if (currentLanguage === defaultLanguage) {
			return selectedMenuItem
		} else {
			// 如果有本地语言菜单项，返回本地语言菜单项
			if (selectedLocalMenuItem) {
				// 确保子菜单结构与默认语言一致
				if (
					selectedMenuItem &&
					Array.isArray(selectedMenuItem.children) &&
					selectedMenuItem.children.length > 0
				) {
					// 保留子菜单结构，但使用本地语言的标签
					return {
						...selectedLocalMenuItem,
						children: selectedMenuItem.children.map((child) => {
							// 查找本地语言中对应的子菜单项
							const localChild = selectedLocalMenuItem.children?.find(
								(localChild) => localChild.id === child.id,
							)

							// 如果找到了本地语言的子菜单项，使用它的标签，否则使用空标签
							return localChild || { ...child, label: "" }
						}),
					}
				}
				return selectedLocalMenuItem
			}
			// 如果没有本地语言菜单项但有默认语言菜单项，返回一个基于默认语言的新菜单项
			else if (selectedMenuItem) {
				// 创建一个新的菜单项，保留结构属性，但清空内容属性
				return {
					...selectedMenuItem,
					label: "", // 清空标签，让用户填写当前语言的标签
					// 保留其他必要的结构属性如 id, href, target 等
				}
			}
			// 如果都没有，返回null
			return null
		}
	}

	// 获取当前编辑的菜单项
	const currentEditingMenuItem = getCurrentEditingMenuItem()

	// 使用原始顺序，不再需要排序
	const sortedMenuItems = [...defaultLanguageMenuItems]

	return {
		isLoading,
		menuItems: sortedMenuItems, // 返回已排序的菜单列表
		selectedMenuItem: currentEditingMenuItem, // 返回当前应该编辑的菜单项
		setSelectedMenuItem, // 保持原有的设置函数名称，以兼容现有代码
		addMenuItem,
		addSubMenuItem,
		updateMenuItem,
		deleteMenuItem,
		reorderMenuItems,
	}
}
