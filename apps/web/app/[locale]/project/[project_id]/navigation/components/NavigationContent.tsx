"use client"

import { useState } from "react"
import {
	<PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
} from "@repo/ui/components"
import { TranslationProvider } from "@/lib/components/translation/TranslationContext"
import HeaderNavigation from "./HeaderNavigation"
import FooterSettings from "./FooterSettings"
import { fetchPost } from "@repo/utils/react"

import { NavigationType } from "../hooks/useNavigationSettings"
import { Language, ProjectLanguage } from "@repo/shared-types"

import { useCallback } from "react"
interface NavigationContentProps {
	projectId: string
	languages: Language[]
	defaultLanguage: ProjectLanguage
}

export default function NavigationContent({
	projectId,
	languages,
	defaultLanguage,
}: NavigationContentProps) {
	const [activeTab, setActiveTab] = useState<NavigationType>(
		NavigationType.HEADER,
	)

	const translate = useCallback(
		async (selectedLanguages: ProjectLanguage[]) =>
			fetchPost("/api/project-locale-site-settings/translate", {
				projectId,
				sourceLocale: defaultLanguage,
				targetLocales: selectedLanguages,
				type: activeTab,
			}),
		[activeTab],
	)

	return (
		<div className="flex-1 overflow-y-auto p-4 pb-20">
			<div className="max-w-6xl mx-auto">
				<TranslationProvider
					languages={languages}
					defaultLanguage={defaultLanguage}
				>
					<Tabs
						defaultValue={NavigationType.HEADER}
						value={activeTab}
						onValueChange={(value) => setActiveTab(value as NavigationType)}
						className="w-full"
					>
						<TabsList className="mb-6 h-12">
							<TabsTrigger
								value={NavigationType.HEADER}
								className="px-10 py-4 text-md"
							>
								头部导航
							</TabsTrigger>
							<TabsTrigger
								value={NavigationType.FOOTER}
								className="px-10 py-4 text-md"
							>
								页脚设置
							</TabsTrigger>
						</TabsList>

						<TabsContent value={NavigationType.HEADER}>
							<HeaderNavigation projectId={projectId} />
						</TabsContent>
						<TabsContent value={NavigationType.FOOTER}>
							<FooterSettings projectId={projectId} />
						</TabsContent>
					</Tabs>
				</TranslationProvider>
			</div>
		</div>
	)
}
